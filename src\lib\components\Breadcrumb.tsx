'use client';

import React from 'react';
import { Breadcrumb as AntBreadcrumb } from 'antd';
import { HomeOutlined } from '@ant-design/icons';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export interface BreadcrumbItem {
  title: string;
  href?: string;
  icon?: React.ReactNode;
}

export interface BreadcrumbProps {
  /**
   * 自定义面包屑项目，如果不提供则根据当前路由自动生成
   */
  items?: BreadcrumbItem[];
  /**
   * 是否显示首页图标
   */
  showHomeIcon?: boolean;
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 自定义类名
   */
  className?: string;
}

/**
 * 路由到面包屑的映射配置
 */
const ROUTE_BREADCRUMB_MAP: Record<string, BreadcrumbItem[]> = {
  '/': [
    { title: '首页', href: '/', icon: <HomeOutlined /> }
  ],
  '/quote': [
    { title: '首页', href: '/', icon: <HomeOutlined /> },
    { title: '包装报价' }
  ],
  '/user': [
    { title: '首页', href: '/', icon: <HomeOutlined /> },
    { title: '用户中心' }
  ],
  '/about': [
    { title: '首页', href: '/', icon: <HomeOutlined /> },
    { title: '关于我们' }
  ],
  '/news': [
    { title: '首页', href: '/', icon: <HomeOutlined /> },
    { title: '新闻中心' }
  ],
  '/login': [
    { title: '首页', href: '/', icon: <HomeOutlined /> },
    { title: '登录' }
  ]
};

/**
 * 根据路径自动生成面包屑项目
 */
function generateBreadcrumbItems(pathname: string): BreadcrumbItem[] {
  // 直接匹配配置
  if (ROUTE_BREADCRUMB_MAP[pathname]) {
    return ROUTE_BREADCRUMB_MAP[pathname];
  }

  // 动态路由处理
  const segments = pathname.split('/').filter(Boolean);
  const items: BreadcrumbItem[] = [
    { title: '首页', href: '/', icon: <HomeOutlined /> }
  ];

  // 构建路径段
  let currentPath = '';
  for (let i = 0; i < segments.length; i++) {
    currentPath += `/${segments[i]}`;
    const isLast = i === segments.length - 1;
    
    // 根据路径段生成标题
    let title = segments[i];
    switch (segments[i]) {
      case 'quote':
        title = '包装报价';
        break;
      case 'user':
        title = '用户中心';
        break;
      case 'about':
        title = '关于我们';
        break;
      case 'news':
        title = '新闻中心';
        break;
      case 'login':
        title = '登录';
        break;
      case 'admin':
        title = '管理后台';
        break;
      case 'dashboard':
        title = '仪表盘';
        break;
      case 'box':
        title = '盒型管理';
        break;
      case 'material':
        title = '材料管理';
        break;
      case 'customFormula':
        title = '自定义公式';
        break;
      case 'craftSalary':
        title = '工艺工价';
        break;
      case 'settings':
        title = '系统设置';
        break;
      case 'users':
        title = '用户管理';
        break;
      default:
        // 保持原始值或进行其他转换
        title = segments[i];
    }

    items.push({
      title,
      href: isLast ? undefined : currentPath
    });
  }

  return items;
}

/**
 * 面包屑导航组件
 */
export default function Breadcrumb({
  items,
  showHomeIcon = true,
  style,
  className
}: BreadcrumbProps) {
  const pathname = usePathname();

  // 使用自定义项目或根据路由自动生成
  const breadcrumbItems = items || generateBreadcrumbItems(pathname);

  // 转换为 Ant Design Breadcrumb 所需的格式
  const antBreadcrumbItems = breadcrumbItems.map((item, index) => {
    const isLast = index === breadcrumbItems.length - 1;
    
    return {
      key: index,
      title: (
        <span style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          {item.icon && showHomeIcon && item.icon}
          {item.href && !isLast ? (
            <Link
              href={item.href}
              style={{
                color: '#666',
                textDecoration: 'none',
                transition: 'color 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = '#FF422D';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = '#666';
              }}
            >
              {item.title}
            </Link>
          ) : (
            <span style={{
              color: isLast ? '#FF422D' : '#666',
              fontWeight: isLast ? '500' : 'normal'
            }}>
              {item.title}
            </span>
          )}
        </span>
      )
    };
  });

  return (
    <AntBreadcrumb
      items={antBreadcrumbItems}
      style={{
        margin: '16px 0',
        fontSize: '14px',
        padding: '8px 0',
        backgroundColor: '#fafafa',
        borderRadius: '4px',
        paddingLeft: '12px',
        paddingRight: '12px',
        border: '1px solid #f0f0f0',
        ...style
      }}
      className={className}
      separator=">"
    />
  );
}
